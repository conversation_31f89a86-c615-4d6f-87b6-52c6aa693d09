<?php

declare(strict_types=1);

namespace App\PostType\Blog\Api\V1\Update;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\RequestBody;
use Apitte\Core\Annotation\Controller\RequestParameter;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Exception\Api\ClientErrorException;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Schema\EndpointParameter;
use App\Api\Decorator\MutationFinder;
use App\Api\Decorator\RequestAuthentication;
use App\Api\V1\Controllers\BaseV1Controller;
use App\Model\Orm\ApiToken\ApiToken;
use App\PostType\Blog\Api\V1\BlogLocalizationInputData;
use App\PostType\Blog\Api\V1\Detail\Response\BlogLocalization;
use App\PostType\Blog\Model\BlogLocalizationFacade;
use App\PostType\Blog\Model\Dto\BlogLocalizationDto;
use App\PostType\Blog\Model\Orm\BlogLocalizationModel;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use function assert;

#[Path('/')]
#[Tag('Blog')]
final class BlogLocalizationUpdateController extends BaseV1Controller
{

	public function __construct(
		private readonly BlogLocalizationRepository $blogLocalizationRepository,
		private readonly BlogLocalizationFacade $blogLocalizationFacade,
		private readonly TreeRepository $treeRepository,
	)
	{
	}

	#[Path('/mutation/{mutationId}/blog/{id}')]
	#[Method('PUT')]
	#[RequestParameter(name: 'id', type: 'int', in: EndpointParameter::IN_PATH, required: true, description: 'Page ID')]
	#[RequestParameter(name: MutationFinder::MUTATION_ID_PARAMETER_NAME, type: 'int', in: EndpointParameter::IN_PATH, required: true, description: 'Mutation id')]
	#[RequestBody(entity: BlogLocalizationInputData::class, required: true)]
	#[Response(description: 'Success', code: '200', entity: BlogLocalization::class)]
	#[Response(description: 'Not found', code: '404')]
	public function put(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$apiToken = $request->getAttribute(RequestAuthentication::API_TOKEN_ATTRIBUTE_NAME);
		assert($apiToken instanceof ApiToken);

		$inputData = $request->getEntity();
		assert($inputData instanceof BlogLocalizationInputData);

		$blogLocalization = $this->blogLocalizationRepository->getBy([
			'id' => $request->getParameter('id'),
			'mutation' => $request->getParameter(MutationFinder::MUTATION_PARAMETER_NAME),
		]);

		if ($blogLocalization === null) {
			throw new ClientErrorException('Not found', ApiResponse::S404_NOT_FOUND);
		}

		assert($blogLocalization instanceof \App\PostType\Blog\Model\Orm\BlogLocalization);

		$this->blogLocalizationFacade->put($blogLocalization, new BlogLocalizationDto(
			isPublic: $inputData->public,
			name: $inputData->name,
			nameAnchor: $inputData->nameAnchor,
			nameTitle: $inputData->nameTitle,
			alias: $inputData->alias,
			cf: ArrayHash::from($inputData->cf),
			cc: ArrayHash::from($inputData->cc),
			commonCf: ArrayHash::from($inputData->commonCf),
			tags: $inputData->tags,
			authors: $inputData->authors,
			categories: $this->treeRepository->findByIds($inputData->categories),
			editor: $apiToken->issuer,
		));

		return $this->jsonResponse((new BlogLocalization($blogLocalization))->toResponse(), $response);
	}

}
