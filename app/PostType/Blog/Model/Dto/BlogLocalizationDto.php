<?php declare(strict_types=1);

namespace App\PostType\Blog\Model\Dto;

use App\Model\Orm\User\User;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\Page\Model\Orm\Tree;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;

final class BlogLocalizationDto
{

	public function __construct(
		public bool $isPublic,
		public string $name,
		public string $nameAnchor,
		public string $nameTitle,
		public string|null $alias,
		public ArrayHash $cf,
		public ArrayHash $cc,
		public ArrayHash $commonCf,
		/** @var BlogTagLocalization[] $tags */
		public array $tags,
		/** @var AuthorLocalization[] $authors */
		public array $authors,
		/** @var Tree[] $categories */
		public array $categories,
		public User $editor,
	)
	{
	}




}
