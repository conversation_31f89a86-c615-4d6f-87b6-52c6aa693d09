{layout $templates.'/@layout-new.latte'}
{var $dataGridShown = true}

{block #content}
	<div class="main__main">
		<div class="main__header">
			{include $templates.'/part/box/header.latte',
				props: [
					title: Blog,
					isPageTitle: true,
					actions: [
						[
							'link' => $presenter->link('import'),
							'text' => 'Import z JSON',
							'class' => 'btn btn--primary',
							'icon' => 'upload'
						]
					]
				]
			}
		</div>
		<div class="main__content scroll">
			{control grid}
		</div>
		<div class="main__content-side scroll">
			{control shellForm}
		</div>
	</div>




