{layout '../../../AdminModule/templates/@layout.latte'}

{block title}Import blogov{/block}

{block content}
<div class="content">
	<div class="content__header">
		<h1 class="content__title">Import blogov z JSON súboru</h1>
		<div class="content__actions">
			<a n:href="default" class="btn btn--grey">
				<span class="btn__text">Späť na zoznam</span>
			</a>
		</div>
	</div>

	<div class="content__body">
		<div class="card">
			<div class="card__header">
				<h2 class="card__title">Nahrať JSON súbor</h2>
			</div>
			<div class="card__body">
				<div class="alert alert--info">
					<p><strong>Formát JSON súboru:</strong></p>
					<p>Súbor musí obsahovať pole objektov s nasledujú<PERSON>u štruktúrou:</p>
					<pre><code>[
  {
    "title": "Názov článku",
    "content_html": "&lt;div&gt;HTML obsah&lt;/div&gt;",
    "annotation": "Krátky popis (voliteľné)",
    "slug": "nazov-clanku",
    "date": "2019-05-23",
    "image": "https://example.com/image.jpg (voliteľné)",
    "tags": ["tag1", "tag2"] (voliteľné)
  }
]</code></pre>
					<p><strong>Maximálna veľkosť súboru:</strong> 16 MB</p>
				</div>

				{form importForm}
					<div class="form-group">
						{label jsonFile class: 'form-label' /}
						{input jsonFile class: 'form-control'}
						<div class="form-help">
							Vyberte JSON súbor s blogmi na import. Súbor môže mať maximálne 16 MB.
						</div>
					</div>

					<div class="form-group">
						{label libraryTreeId class: 'form-label' /}
						{input libraryTreeId class: 'form-control'}
						<div class="form-help">
							ID kategórie v knižnici obrázkov, kam sa uložia stiahnuté obrázky. Predvolená hodnota je 1 (root kategória).
						</div>
					</div>

					<div class="form-actions">
						{input import class: 'btn btn--success'}
					</div>
				{/form}
			</div>
		</div>

		{if isset($importResults)}
		<div class="card">
			<div class="card__header">
				<h2 class="card__title">Výsledky importu</h2>
			</div>
			<div class="card__body">
				<div class="stats">
					<div class="stats__item">
						<div class="stats__number">{$importResults['total']}</div>
						<div class="stats__label">Celkom</div>
					</div>
					<div class="stats__item stats__item--success">
						<div class="stats__number">{$importResults['imported']}</div>
						<div class="stats__label">Importované</div>
					</div>
					<div class="stats__item stats__item--warning">
						<div class="stats__number">{$importResults['skipped']}</div>
						<div class="stats__label">Preskočené</div>
					</div>
					<div class="stats__item stats__item--error">
						<div class="stats__number">{count($importResults['errors'])}</div>
						<div class="stats__label">Chyby</div>
					</div>
				</div>

				{if $importResults['imported_blogs']}
				<div class="section">
					<h3>Úspešne importované blogy:</h3>
					<ul class="list">
						{foreach $importResults['imported_blogs'] as $blog}
						<li class="list__item">
							<a n:href="edit, $blog['id']" class="link">
								{$blog['title']} ({$blog['slug']})
							</a>
						</li>
						{/foreach}
					</ul>
				</div>
				{/if}

				{if $importResults['errors']}
				<div class="section">
					<h3>Chyby pri importe:</h3>
					<div class="errors">
						{foreach $importResults['errors'] as $error}
						<div class="alert alert--error">
							<strong>Riadok {$error['index'] + 1}:</strong> {$error['title']}<br>
							<em>{$error['error']}</em>
						</div>
						{/foreach}
					</div>
				</div>
				{/if}
			</div>
		</div>
		{/if}
	</div>
</div>

<style>
.stats {
	display: flex;
	gap: 1rem;
	margin-bottom: 2rem;
}

.stats__item {
	background: #f8f9fa;
	padding: 1rem;
	border-radius: 4px;
	text-align: center;
	flex: 1;
}

.stats__item--success {
	background: #d4edda;
	color: #155724;
}

.stats__item--warning {
	background: #fff3cd;
	color: #856404;
}

.stats__item--error {
	background: #f8d7da;
	color: #721c24;
}

.stats__number {
	font-size: 2rem;
	font-weight: bold;
	margin-bottom: 0.5rem;
}

.stats__label {
	font-size: 0.875rem;
	text-transform: uppercase;
	letter-spacing: 0.05em;
}

.section {
	margin-top: 2rem;
}

.section h3 {
	margin-bottom: 1rem;
	color: #333;
}

.list {
	list-style: none;
	padding: 0;
}

.list__item {
	padding: 0.5rem 0;
	border-bottom: 1px solid #eee;
}

.list__item:last-child {
	border-bottom: none;
}

.errors .alert {
	margin-bottom: 1rem;
}

.errors .alert:last-child {
	margin-bottom: 0;
}

pre {
	background: #f8f9fa;
	padding: 1rem;
	border-radius: 4px;
	overflow-x: auto;
	font-size: 0.875rem;
}

.form-help {
	font-size: 0.875rem;
	color: #6c757d;
	margin-top: 0.25rem;
}
</style>
{/block}
