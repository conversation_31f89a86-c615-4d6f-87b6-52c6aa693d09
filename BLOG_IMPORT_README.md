# Blog Import Flow - Dokumentácia

## Prehľad

Implementovaný bol kompletný flow na import blogov z JSON súboru s podporou:
- Importu až 4MB JSON súborov
- Automatického sťahovania a ukladania obrázkov pomocou LibraryImage
- Vytvárania blog postov a ich lokalizácií
- Spracovania tagov (vytvorenie nových alebo použitie existujúcich)
- Detailného reportingu výsledkov importu

## Štruktúra JSON súboru

JSON súbor musí obsahovať pole objektov s nasledujúcou štruktúrou:

```json
[
  {
    "title": "Názov článku",
    "content_html": "<div>HTML obsah článku</div>",
    "annotation": "Krátky popis článku (voliteľné)",
    "slug": "nazov-clanku-url",
    "date": "2019-05-23",
    "image": "https://example.com/image.jpg (voliteľné)",
    "tags": ["tag1", "tag2"] (voliteľné)
  }
]
```

### Povinné polia:
- `title` - Názov článku (string)
- `content_html` - HTML obsah článku (string)
- `slug` - URL slug článku (string, musí byť jedinečný)
- `date` - Dátum publikácie vo formáte YYYY-MM-DD (string)

### Voliteľné polia:
- `annotation` - Krátky popis článku (string)
- `image` - URL obrázka na stiahnutie (string)
- `tags` - Pole tagov (array of strings)

## Použitie

### 1. Prístup k importu
- Prejdite do admin sekcie Blog
- Kliknite na tlačidlo "Import z JSON"

### 2. Nahranie súboru
- Vyberte JSON súbor (max. 16MB)
- Zadajte ID kategórie pre obrázky (predvolene 1 = root kategória)
- Kliknite "Importovať blogy"

### 3. Výsledky
Po dokončení importu sa zobrazí:
- Celkový počet spracovaných položiek
- Počet úspešne importovaných blogov
- Počet preskočených položiek
- Zoznam chýb s detailmi

## Technické detaily

### Implementované súbory:

1. **BlogLocalizationImportParser.php**
   - Parsovanie a validácia JSON súboru
   - Kontrola povinných polí a formátov

2. **BlogImportService.php**
   - Hlavná logika importu
   - Sťahovanie obrázkov z URL
   - Vytvorenie blog entít a lokalizácií
   - Spracovanie tagov

3. **BlogPresenter.php** (rozšírený)
   - Import akcia a formulár
   - Spracovanie nahraného súboru

4. **import.latte**
   - Template pre import formulár a výsledky

5. **config.neon** (aktualizovaný)
   - Registrácia BlogImportService

### Docker konfigurácia
- PHP kontajner je nakonfigurovaný pre 16MB+ uploady
- `upload_max_filesize = 16M`
- `post_max_size = 20M`
- `max_execution_time = 300`

### Makefile príkazy
```bash
# Force recreate všetkých kontajnerov
make force-recreate

# Force recreate len app kontajnera
make force-recreate-app
```

## Spracovanie obrázkov

### Automatické sťahovanie
- Obrázky sa sťahujú z poskytnutých URL
- Ukladajú sa do zadanej LibraryTree kategórie
- Podporované formáty: JPG, PNG, GIF, WebP
- Timeout: 30 sekúnd na stiahnutie

### Chybové stavy
- Neplatné URL sa preskočia s logovaním chyby
- Nedostupné obrázky sa preskočia s logovaním chyby
- Import blogu pokračuje aj bez obrázka

## Spracovanie tagov

### Automatické vytvorenie
- Nové tagy sa automaticky vytvárajú
- Existujúce tagy sa znovu použijú (podľa slug)
- Tagy sa vytvárajú pre aktuálnu mutáciu

### Slug generovanie
- Automatické webalizovanie názvov tagov
- Kontrola duplicít v rámci mutácie

## Chybové stavy a riešenia

### Časté chyby:

1. **"Blog with slug 'xyz' already exists"**
   - Riešenie: Zmeňte slug v JSON alebo odstráňte existujúci blog

2. **"Invalid image URL"**
   - Riešenie: Skontrolujte formát URL obrázka

3. **"Failed to download image"**
   - Riešenie: Skontrolujte dostupnosť obrázka na URL

4. **"Library tree with ID X not found"**
   - Riešenie: Použite existujúce ID kategórie alebo vytvorte novú

### Logovanie
- Všetky chyby sa logujú do Tracy logov
- Import chyby: `blog-import-error`
- Obrázok chyby: `blog-import-image-error`

## Testovanie

### Test súbor
V root adresári je pripravený `test-blog-import.json` s ukážkovými dátami.

### Postup testovania:
1. Spustite `make force-recreate-app` pre aplikovanie PHP konfigurácií
2. Prejdite do admin sekcie Blog
3. Kliknite "Import z JSON"
4. Nahrajte `test-blog-import.json`
5. Skontrolujte výsledky

## Bezpečnosť

### Validácie:
- Kontrola typu súboru (JSON)
- Validácia štruktúry dát
- Sanitizácia názvov súborov
- Kontrola veľkosti súboru
- Timeout pre sťahovanie obrázkov

### Limity:
- Maximálna veľkosť súboru: 16MB
- Timeout pre sťahovanie obrázka: 30s
- Maximálny čas vykonávania: 300s

## Rozšírenia

### Možné budúce vylepšenia:
1. Batch processing pre veľké súbory
2. Progress bar pre dlhé importy
3. Podpora ďalších formátov obrázkov
4. Import z CSV súborov
5. Automatické optimalizácie obrázkov
6. Podpora pre viacjazyčné importy

## Podpora

Pri problémoch skontrolujte:
1. Tracy logy v `log/` adresári
2. PHP error logy
3. Docker kontajner logy: `docker compose logs app`

Pre ďalšie otázky kontaktujte vývojový tím.
3. Kliknite "Import z JSON"
4. Nahrajte `test-blog-import.json`
5. Skontrolujte výsledky

## Bezpečnosť

### Validácie:
- Kontrola typu súboru (JSON)
- Validácia štruktúry dát
- Sanitizácia názvov súborov
- Kontrola veľkosti súboru
- Timeout pre sťahovanie obrázkov

### Limity:
- Maximálna veľkosť súboru: 16MB
- Timeout pre sťahovanie obrázka: 30s
- Maximálny čas vykonávania: 300s
